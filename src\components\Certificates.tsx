"use client";

import { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import {
  Award,
  Calendar,
  ExternalLink,
  Eye,
  Download,
  // Search,
  Filter,
  ChevronDown,
  X,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import Image from "next/image";

gsap.registerPlugin(ScrollTrigger);

const certificates = [
  {
    id: 1,
    title: "Udacity – Web Development",
    issuer: "Udacity",
    date: "2023",
    description:
      "Comprehensive web development program covering modern frameworks and best practices.",
    icon: "🎓",
    color: "from-blue-500 to-indigo-600",
  },
  {
    id: 2,
    title: "Python & Django Course",
    issuer: "Professional Training",
    date: "2023",
    description:
      "Advanced Python programming and Django framework for web development.",
    icon: "🐍",
    color: "from-green-500 to-emerald-600",
  },
  {
    id: 3,
    title: "Embedded Systems Diploma",
    issuer: "Technical Institute",
    date: "2022",
    description: "Hardware programming and embedded systems development.",
    icon: "⚡",
    color: "from-orange-500 to-red-600",
  },
  {
    id: 4,
    title: "Route Academy – Frontend Development",
    issuer: "Route Academy",
    date: "2023",
    description:
      "Modern frontend development with React, JavaScript, and responsive design.",
    icon: "⚛️",
    color: "from-purple-500 to-pink-600",
  },
];

// Certificate image interface
interface CertificateImage {
  title: string;
  issuer: string;
  date: string;
  url: string;
  filename: string;
}

// Helper function to parse certificate filename
const parseCertificateFilename = (
  filename: string
): Omit<CertificateImage, "url" | "filename"> => {
  const basename = filename.replace(/\.[^/.]+$/, ""); // Remove extension

  // Pattern 1: YYYY-MM - ISSUER - TITLE
  const pattern1 = /^(\d{4})-(\d{2})\s*-\s*(.+?)\s*-\s*(.+)$/i;
  const match1 = basename.match(pattern1);

  if (match1) {
    const [, year, month, issuer, title] = match1;
    return {
      title: title.trim(),
      issuer: issuer.trim(),
      date: `${year}-${month}`,
    };
  }

  // Pattern 2: ISSUER - TITLE - YYYY
  const pattern2 = /^(.+?)\s*-\s*(.+?)\s*-\s*(\d{4})$/i;
  const match2 = basename.match(pattern2);

  if (match2) {
    const [, issuer, title, year] = match2;
    return {
      title: title.trim(),
      issuer: issuer.trim(),
      date: year,
    };
  }

  // Fallback
  return {
    title: basename,
    issuer: "Certificate",
    date: "Unknown",
  };
};

// Helper function to sort certificates
const sortCertificates = (
  certs: CertificateImage[],
  sortBy: string
): CertificateImage[] => {
  return [...certs].sort((a, b) => {
    switch (sortBy) {
      case "newest":
        if (a.date === "Unknown" && b.date === "Unknown")
          return a.filename.localeCompare(b.filename);
        if (a.date === "Unknown") return 1;
        if (b.date === "Unknown") return -1;
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      case "oldest":
        if (a.date === "Unknown" && b.date === "Unknown")
          return a.filename.localeCompare(b.filename);
        if (a.date === "Unknown") return 1;
        if (b.date === "Unknown") return -1;
        return new Date(a.date).getTime() - new Date(b.date).getTime();
      case "az":
        return a.title.localeCompare(b.title);
      case "za":
        return b.title.localeCompare(a.title);
      default:
        return 0;
    }
  });
};

const Certificates = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const cardsRef = useRef<HTMLDivElement[]>([]);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate timeline line
      if (timelineRef.current) {
        gsap.fromTo(
          timelineRef.current,
          { scaleY: 0 },
          {
            scaleY: 1,
            duration: 2,
            ease: "power2.out",
            scrollTrigger: {
              trigger: timelineRef.current,
              start: "top 80%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          }
        );
      }

      // Animate certificate cards
      cardsRef.current.forEach((card, index) => {
        if (card) {
          gsap.fromTo(
            card,
            {
              opacity: 0,
              x: index % 2 === 0 ? -100 : 100,
              scale: 0.8,
            },
            {
              opacity: 1,
              x: 0,
              scale: 1,
              duration: 1,
              ease: "power3.out",
              scrollTrigger: {
                trigger: card,
                start: "top 80%",
                end: "bottom 20%",
                toggleActions: "play none none reverse",
              },
              delay: index * 0.2,
            }
          );
        }
      });
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const addToRefs = (el: HTMLDivElement | null) => {
    if (el && !cardsRef.current.includes(el)) {
      cardsRef.current.push(el);
    }
  };

  // Certificate Images Gallery Component
  const CertificateImagesGallery = () => {
    const [certificateImages, setCertificateImages] = useState<
      CertificateImage[]
    >([]);
    const [filteredCertificates, setFilteredCertificates] = useState<
      CertificateImage[]
    >([]);
    const [selectedIssuer, setSelectedIssuer] = useState<string>("all");
    const [sortBy, setSortBy] = useState<string>("newest");
    // const [searchTerm, setSearchTerm] = useState<string>("");
    const [showSortDropdown, setShowSortDropdown] = useState<boolean>(false);
    const [selectedCertificate, setSelectedCertificate] =
      useState<CertificateImage | null>(null);
    const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);
    const [imageLoading, setImageLoading] = useState<{
      [key: string]: boolean;
    }>({});

    const galleryRef = useRef<HTMLDivElement>(null);
    const galleryCardsRef = useRef<HTMLDivElement[]>([]);
    const sortDropdownRef = useRef<HTMLDivElement>(null);

    // GSAP animations for gallery
    useEffect(() => {
      if (galleryRef.current && filteredCertificates.length > 0) {
        const ctx = gsap.context(() => {
          // Animate gallery header
          gsap.fromTo(
            galleryRef.current?.querySelector(".gallery-header"),
            { opacity: 0, y: 30 },
            {
              opacity: 1,
              y: 0,
              duration: 1,
              ease: "power3.out",
              scrollTrigger: {
                trigger: galleryRef.current,
                start: "top 80%",
                end: "bottom 20%",
                toggleActions: "play none none reverse",
              },
            }
          );

          // Animate filters
          gsap.fromTo(
            galleryRef.current?.querySelector(".gallery-filters"),
            { opacity: 0, y: 20 },
            {
              opacity: 1,
              y: 0,
              duration: 0.8,
              ease: "power3.out",
              delay: 0.2,
              scrollTrigger: {
                trigger: galleryRef.current,
                start: "top 80%",
                end: "bottom 20%",
                toggleActions: "play none none reverse",
              },
            }
          );
        }, galleryRef);

        return () => ctx.revert();
      }
    }, [filteredCertificates]);

    // Load certificate images on mount
    useEffect(() => {
      const loadCertificateImages = async () => {
        try {
          // Since we're using Next.js and the images are in public folder,
          // we'll manually create the list based on known files
          const actualFiles = [
            "cer1.png",
            "cer2.png",
            "cer3.jpg",
            "cer4.jpg",
            "cer5.jpg",
            // "cer6.jpg",
            "cer7.png",
            "cer8.png",
            "cer9.png",
          ];

          // Create sample data with proper naming for demonstration
          const sampleData = [
            {
              title: "Frontend Development",
              issuer: "Route Academy",
              date: "2023-11",
            },
            {
              title: "Web Development Nanodegree",
              issuer: "Udacity",
              date: "2023-12",
            },
            // {
            //   title: "FreeLance",
            //   issuer: "Itida",
            //   date: "2023-10",
            // },
            {
              title: "Embedded Systems Diploma",
              issuer: "Technical Institute",
              date: "2022-08",
            },
            {
              title: "FreeLance",
              issuer: "Itida",
              date: "2023",
            },
            { title: "Azure Fundamentals", issuer: "Microsoft", date: "2023" },
            {
              title: "ES6",
              issuer: "Mahara Tech",
              date: "2022",
            },
            {
              title: "HTML",
              issuer: "Mahara Tech",
              date: "2023",
            },
            {
              title: "Python",
              issuer: "Mahara Tech",
              date: "2022",
            },
          ];

          const certificates: CertificateImage[] = actualFiles.map(
            (filename, index) => {
              const data = sampleData[index] || {
                title: filename,
                issuer: "Certificate",
                date: "Unknown",
              };
              return {
                title: data.title,
                issuer: data.issuer,
                date: data.date,
                url: `/assets/Certifications/${filename}`,
                filename,
              };
            }
          );

          setCertificateImages(certificates);
          setFilteredCertificates(sortCertificates(certificates, "newest"));
        } catch (error) {
          console.error("Error loading certificate images:", error);
        }
      };

      loadCertificateImages();
    }, []);

    // Filter and sort certificates
    // useEffect(() => {
    //   let filtered = certificateImages;

    //   // Filter by issuer
    //   if (selectedIssuer !== "all") {
    //     filtered = filtered.filter((cert) => cert.issuer === selectedIssuer);
    //   }

    //   // Filter by search term
    //   if (searchTerm) {
    //     filtered = filtered.filter(
    //       (cert) =>
    //         cert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    //         cert.issuer.toLowerCase().includes(searchTerm.toLowerCase())
    //     );
    //   }

    //   // Sort
    //   filtered = sortCertificates(filtered, sortBy);

    //   setFilteredCertificates(filtered);
    // }, [certificateImages, selectedIssuer, sortBy, searchTerm]);

    // Click outside handler for sort dropdown
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          sortDropdownRef.current &&
          !sortDropdownRef.current.contains(event.target as Node)
        ) {
          setShowSortDropdown(false);
        }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    // Get unique issuers for filter chips
    const uniqueIssuers = Array.from(
      new Set(certificateImages.map((cert) => cert.issuer))
    );

    return (
      <div ref={galleryRef} className="mt-20">
        {/* Gallery Header */}
        <div className="gallery-header text-center mb-12">
          <h3 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            <span className="bg-gradient-to-r from-emerald-400 to-cyan-400 bg-clip-text text-transparent">
              Certificate Gallery
            </span>
          </h3>
          <p className="text-slate-400 text-lg max-w-2xl mx-auto">
            Visual collection of my professional certifications and achievements
          </p>
        </div>

        {/* Certificates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {filteredCertificates.map((cert, index) => (
            <CertificateCard
              key={cert.filename}
              certificate={cert}
              index={index}
              onView={() => {
                setSelectedCertificate(cert);
                setCurrentImageIndex(filteredCertificates.indexOf(cert));
              }}
              imageLoading={imageLoading}
              setImageLoading={setImageLoading}
            />
          ))}
        </div>

        {/* Empty State */}
        {filteredCertificates.length === 0 && (
          <div className="text-center py-12">
            <div className="text-slate-400 text-lg mb-2">
              No certificates found
            </div>
            <div className="text-slate-500 text-sm">
              Try adjusting your search or filter criteria
            </div>
          </div>
        )}

        {/* Lightbox Modal */}
        {selectedCertificate && (
          <CertificateLightbox
            certificate={selectedCertificate}
            certificates={filteredCertificates}
            currentIndex={currentImageIndex}
            onClose={() => setSelectedCertificate(null)}
            onNext={() => {
              const nextIndex =
                (currentImageIndex + 1) % filteredCertificates.length;
              setCurrentImageIndex(nextIndex);
              setSelectedCertificate(filteredCertificates[nextIndex]);
            }}
            onPrev={() => {
              const prevIndex =
                currentImageIndex === 0
                  ? filteredCertificates.length - 1
                  : currentImageIndex - 1;
              setCurrentImageIndex(prevIndex);
              setSelectedCertificate(filteredCertificates[prevIndex]);
            }}
          />
        )}
      </div>
    );
  };

  // Certificate Card Component
  const CertificateCard = ({
    certificate,
    index,
    onView,
    imageLoading,
    setImageLoading,
  }: {
    certificate: CertificateImage;
    index: number;
    onView: () => void;
    imageLoading: { [key: string]: boolean };
    setImageLoading: React.Dispatch<
      React.SetStateAction<{ [key: string]: boolean }>
    >;
  }) => {
    const cardRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      if (cardRef.current) {
        gsap.fromTo(
          cardRef.current,
          {
            opacity: 0,
            y: 50,
            scale: 0.9,
          },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.8,
            ease: "power3.out",
            delay: index * 0.1,
            scrollTrigger: {
              trigger: cardRef.current,
              start: "top 80%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          }
        );
      }
    }, [index]);

    const handleImageLoad = () => {
      setImageLoading((prev) => ({ ...prev, [certificate.filename]: false }));
    };

    const handleImageLoadStart = () => {
      setImageLoading((prev) => ({ ...prev, [certificate.filename]: true }));
    };

    return (
      <div
        ref={cardRef}
        className="group relative bg-white/10 dark:bg-white/5 backdrop-blur-md rounded-2xl shadow-lg p-4 transition-transform hover:scale-[1.02] hover:shadow-xl border border-white/10 overflow-hidden"
      >
        {/* Image Container */}
        <div className="relative aspect-[16/10] rounded-xl overflow-hidden mb-4 bg-slate-800/50">
          {/* Loading Skeleton */}
          {imageLoading[certificate.filename] && (
            <div className="absolute inset-0 bg-gradient-to-r from-slate-700 via-slate-600 to-slate-700 animate-pulse rounded-xl" />
          )}

          <Image
            src={certificate.url}
            alt={`${certificate.title} – ${certificate.issuer} certificate`}
            fill
            className="object-contain transition-transform duration-300 group-hover:scale-105"
            loading="lazy"
            onLoadStart={handleImageLoadStart}
            onLoad={handleImageLoad}
            onError={() =>
              setImageLoading((prev) => ({
                ...prev,
                [certificate.filename]: false,
              }))
            }
          />

          {/* Hover Overlay */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex gap-2">
              <button
                onClick={onView}
                className="p-3 bg-white/90 backdrop-blur-sm rounded-full text-slate-800 hover:bg-white transition-all duration-300 transform hover:scale-110"
                aria-label="View certificate"
              >
                <Eye className="w-5 h-5" />
              </button>
              <a
                href={certificate.url}
                target="_blank"
                rel="noopener noreferrer"
                download
                className="p-3 bg-white/90 backdrop-blur-sm rounded-full text-slate-800 hover:bg-white transition-all duration-300 transform hover:scale-110"
                aria-label="Download certificate"
              >
                <Download className="w-5 h-5" />
              </a>
            </div>
          </div>
        </div>

        {/* Certificate Info */}
        <div className="space-y-2">
          <h4 className="font-semibold text-white text-lg leading-tight line-clamp-2">
            {certificate.title}
          </h4>
          <p className="text-slate-400 text-sm">{certificate.issuer}</p>
          {certificate.date !== "Unknown" && (
            <p className="text-slate-500 text-xs italic">{certificate.date}</p>
          )}
        </div>

        {/* Actions Row */}
        <div className="flex gap-2 mt-4">
          <button
            onClick={onView}
            className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-emerald-500/20 text-emerald-400 rounded-lg text-sm font-medium hover:bg-emerald-500/30 transition-all duration-300"
          >
            <Eye className="w-4 h-4" />
            View
          </button>
          <a
            href={certificate.url}
            target="_blank"
            rel="noopener noreferrer"
            download
            className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-blue-500/20 text-blue-400 rounded-lg text-sm font-medium hover:bg-blue-500/30 transition-all duration-300"
          >
            <Download className="w-4 h-4" />
            Original
          </a>
        </div>
      </div>
    );
  };

  // Certificate Lightbox Component
  const CertificateLightbox = ({
    certificate,
    certificates,
    currentIndex,
    onClose,
    onNext,
    onPrev,
  }: {
    certificate: CertificateImage;
    certificates: CertificateImage[];
    currentIndex: number;
    onClose: () => void;
    onNext: () => void;
    onPrev: () => void;
  }) => {
    const modalRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
        switch (e.key) {
          case "Escape":
            onClose();
            break;
          case "ArrowLeft":
            onPrev();
            break;
          case "ArrowRight":
            onNext();
            break;
        }
      };

      document.addEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "hidden";

      return () => {
        document.removeEventListener("keydown", handleKeyDown);
        document.body.style.overflow = "unset";
      };
    }, [onClose, onNext, onPrev]);

    useEffect(() => {
      if (modalRef.current) {
        gsap.fromTo(
          modalRef.current,
          { opacity: 0, scale: 0.9 },
          { opacity: 1, scale: 1, duration: 0.3, ease: "power2.out" }
        );
      }
    }, [certificate]);

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm">
        <div
          ref={modalRef}
          className="relative max-w-4xl max-h-[90vh] w-full bg-slate-900/95 backdrop-blur-md rounded-2xl border border-white/10 overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/10">
            <div>
              <h3 className="text-xl font-semibold text-white mb-1">
                {certificate.title}
              </h3>
              <p className="text-slate-400 text-sm">
                {certificate.issuer} • {certificate.date}
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-slate-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300"
              aria-label="Close modal"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Image */}
          <div className="relative aspect-[4/3] bg-slate-800/50">
            <Image
              src={certificate.url}
              alt={`${certificate.title} – ${certificate.issuer} certificate`}
              fill
              className="object-contain"
              priority
            />
          </div>

          {/* Navigation and Actions */}
          <div className="flex items-center justify-between p-6 border-t border-white/10">
            <div className="flex items-center gap-4">
              {/* Navigation */}
              {certificates.length > 1 && (
                <div className="flex items-center gap-2">
                  <button
                    onClick={onPrev}
                    className="p-2 text-slate-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300"
                    aria-label="Previous certificate"
                  >
                    <ChevronLeft className="w-5 h-5" />
                  </button>
                  <span className="text-slate-400 text-sm">
                    {currentIndex + 1} of {certificates.length}
                  </span>
                  <button
                    onClick={onNext}
                    className="p-2 text-slate-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300"
                    aria-label="Next certificate"
                  >
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              <a
                href={certificate.url}
                target="_blank"
                rel="noopener noreferrer"
                download
                className="flex items-center gap-2 px-4 py-2 bg-emerald-500 text-white rounded-lg font-medium hover:bg-emerald-600 transition-all duration-300"
              >
                <Download className="w-4 h-4" />
                Download
              </a>
            </div>
          </div>
        </div>

        {/* Background Click to Close */}
        <div
          className="absolute inset-0 -z-10"
          onClick={onClose}
          aria-label="Close modal"
        />
      </div>
    );
  };

  return (
    <section ref={sectionRef} className="py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900"></div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4">
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Certificates & Education
            </span>
          </h2>
          <p className="text-slate-400 text-lg max-w-2xl mx-auto">
            Continuous learning and professional development in cutting-edge
            technologies
          </p>
        </div>

        {/* Timeline */}
        <div className="relative max-w-4xl mx-auto">
          {/* Timeline Line */}
          <div
            ref={timelineRef}
            className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-purple-500 to-pink-500 origin-top"
            style={{ height: "100%" }}
          ></div>

          {/* Certificate Cards */}
          <div className="space-y-12">
            {certificates.map((cert, index) => (
              <div
                key={cert.id}
                ref={addToRefs}
                className={`relative flex items-center ${
                  index % 2 === 0 ? "justify-start" : "justify-end"
                }`}
              >
                {/* Timeline Dot */}
                <div className="absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-white rounded-full border-4 border-purple-500 z-10 shadow-lg"></div>

                {/* Certificate Card */}
                <div
                  className={`w-full max-w-md ${
                    index % 2 === 0 ? "mr-auto pr-8" : "ml-auto pl-8"
                  }`}
                >
                  <div className="group relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 hover:border-white/20 transition-all duration-300 transform hover:scale-105">
                    {/* Icon and Gradient */}
                    <div
                      className={`absolute -top-4 ${
                        index % 2 === 0 ? "left-6" : "right-6"
                      } w-12 h-12 bg-gradient-to-r ${
                        cert.color
                      } rounded-full flex items-center justify-center text-2xl shadow-lg`}
                    >
                      {cert.icon}
                    </div>

                    <div className="pt-6">
                      {/* Date */}
                      <div className="flex items-center gap-2 text-slate-400 text-sm mb-2">
                        <Calendar className="w-4 h-4" />
                        <span>{cert.date}</span>
                      </div>

                      {/* Title */}
                      <h3 className="text-xl font-bold text-white mb-2 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-pink-400 group-hover:bg-clip-text transition-all duration-300">
                        {cert.title}
                      </h3>

                      {/* Issuer */}
                      <p className="text-purple-400 font-medium mb-3">
                        {cert.issuer}
                      </p>

                      {/* Description */}
                      <p className="text-slate-400 text-sm leading-relaxed mb-4">
                        {cert.description}
                      </p>

                      {/* Badge */}
                      <div className="flex items-center gap-2">
                        <Award className="w-4 h-4 text-yellow-400" />
                        <span className="text-yellow-400 text-sm font-medium">
                          Certified
                        </span>
                      </div>
                    </div>

                    {/* Hover Effect */}
                    <div
                      className={`absolute inset-0 bg-gradient-to-r ${cert.color} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-300`}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">4+</div>
            <div className="text-slate-400 text-sm">Certificates</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">2+</div>
            <div className="text-slate-400 text-sm">Years Learning</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">10+</div>
            <div className="text-slate-400 text-sm">Technologies</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">100%</div>
            <div className="text-slate-400 text-sm">Commitment</div>
          </div>
        </div>

        {/* Certificate Images Gallery */}
        <CertificateImagesGallery />
      </div>
    </section>
  );
};

export default Certificates;
